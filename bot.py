import discord
from discord.ext import commands
import asyncio
import aiohttp
from datetime import datetime, timedelta
import pytz
from typing import Dict, Optional, Any, List
import os
import signal
import sys
import random
import json
from dotenv import load_dotenv
from enum import Enum
import config
import traceback

# Load environment variables
load_dotenv()

# Initialize the bot with intents and command prefix
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(
    command_prefix=commands.when_mentioned_or('!'),
    intents=intents,
    help_command=None  # Disable default help command
)

# Constants
IRAN_TIMEZONE = pytz.timezone('Asia/Tehran')
GW2_API_BASE = "https://api.guildwars2.com/v2"
GW2_WORLD_BOSSES = f"{GW2_API_BASE}/worldbosses"
GW2_EVENTS = f"{GW2_API_BASE}/events"
GW2_MAPS = f"{GW2_API_BASE}/maps"

# Cache for boss data and countdown settings
boss_cache = {}
active_countdowns = {}  # {message_id: {'boss_id': str, 'channel_id': int, 'next_spawn': datetime}}
last_fetch = None
CACHE_DURATION = 300  # 5 minutes in seconds
COUNTDOWN_UPDATE_INTERVAL = 30  # seconds between countdown updates

# API Configuration Constants
API_MAX_RETRIES = 3
API_CIRCUIT_BREAKER_THRESHOLD = 5
API_CIRCUIT_BREAKER_TIMEOUT = 300  # 5 minutes
API_HEALTH_CHECK_INTERVAL = 120    # 2 minutes
API_REQUEST_TIMEOUT = 10           # 10 seconds
CACHE_EXTENDED_DURATION = 1800     # 30 minutes during outages

# World Boss Data - Accurate schedule information based on official GW2 data
# All times are in UTC and converted to Iran timezone for display
WORLD_BOSSES = {
    # Major World Bosses (Every 3 hours)
    "tequatl_the_sunless": {
        "name": "Tequatl the Sunless",
        "map": "Sparkfly Fen",
        "schedule": [0, 3, 6, 9, 12, 15, 18, 21],  # Every 3 hours starting at 00:00 UTC
        "duration": 30,  # Event duration in minutes
        "emoji": "🐲",
        "difficulty": "Hard",
        "pre_event_time": 15  # Pre-events start 15 minutes before
    },
    "the_shatterer": {
        "name": "The Shatterer",
        "map": "Blazeridge Steppes",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 15,
        "emoji": "🐉",
        "difficulty": "Medium",
        "pre_event_time": 5
    },
    "triple_trouble": {
        "name": "Triple Trouble",
        "map": "Bloodtide Coast",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Same as Shatterer
        "duration": 20,
        "emoji": "🐍",
        "difficulty": "Hard",
        "pre_event_time": 10
    },
    "karka_queen": {
        "name": "Karka Queen",
        "map": "Southsun Cove",
        "schedule": [2, 6, 10, 14, 18, 22],  # Every 4 hours starting at 02:00 UTC
        "duration": 15,
        "emoji": "🦀",
        "difficulty": "Medium",
        "pre_event_time": 5
    },

    # Regular World Bosses (Every 2 hours)
    "shadow_behemoth": {
        "name": "Shadow Behemoth",
        "map": "Queensdale",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "👹",
        "difficulty": "Easy",
        "pre_event_time": 5
    },
    "svanir_shaman_chief": {
        "name": "Svanir Shaman Chief",
        "map": "Wayfarer Foothills",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "❄️",
        "difficulty": "Easy",
        "pre_event_time": 5
    },
    "fire_elemental": {
        "name": "Fire Elemental",
        "map": "Metrica Province",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "🔥",
        "difficulty": "Easy",
        "pre_event_time": 5
    },
    "the_frozen_maw": {
        "name": "The Frozen Maw",
        "map": "Wayfarer Foothills",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "🧊",
        "difficulty": "Easy",
        "pre_event_time": 5
    },
    "jungle_wurm": {
        "name": "Jungle Wurm",
        "map": "Caledon Forest",
        "schedule": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23],  # Every 2 hours, offset by 1
        "duration": 15,
        "emoji": "🪱",
        "difficulty": "Easy",
        "pre_event_time": 5
    },
    "great_jungle_wurm": {
        "name": "Great Jungle Wurm",
        "map": "Bloodtide Coast",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 15,
        "emoji": "🐛",
        "difficulty": "Medium",
        "pre_event_time": 10
    },

    # Other Bosses
    "admiral_taidha_covington": {
        "name": "Admiral Taidha Covington",
        "map": "Bloodtide Coast",
        "schedule": [0, 3, 6, 9, 12, 15, 18, 21],  # Every 3 hours with Tequatl
        "duration": 15,
        "emoji": "🏴‍☠️",
        "difficulty": "Medium",
        "pre_event_time": 10
    },
    "megadestroyer": {
        "name": "Megadestroyer",
        "map": "Mount Maelstrom",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 15,
        "emoji": "🤖",
        "difficulty": "Medium",
        "pre_event_time": 5
    },
    "modniir_ulgoth": {
        "name": "Modniir Ulgoth",
        "map": "Harathi Hinterlands",
        "schedule": [2, 5, 8, 11, 14, 17, 20, 23],  # Every 3 hours starting at 02:00 UTC
        "duration": 15,
        "emoji": "🐂",
        "difficulty": "Medium",
        "pre_event_time": 5
    }
}

# Alert tracking
sent_alerts = {}  # {boss_id: {'10min': timestamp, '5min': timestamp, 'spawn': timestamp}}

# API Status Enum
class APIStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    DOWN = "down"

class GW2APIManager:
    """Manages GW2 API requests with circuit breaker pattern and health monitoring"""

    def __init__(self):
        self.session = None
        self.status = APIStatus.HEALTHY
        self.failure_count = 0
        self.last_failure_time = None
        self.circuit_open = False

    async def _get_session(self):
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=API_REQUEST_TIMEOUT)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _make_request(self, url: str, retries: int = API_MAX_RETRIES) -> Optional[Dict]:
        """Make HTTP request with retry logic"""
        session = await self._get_session()

        for attempt in range(retries + 1):
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        self._record_success()
                        return data
                    elif response.status == 429:  # Rate limited
                        wait_time = min(2 ** attempt, 60)
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        self._record_failure()
                        return None

            except asyncio.TimeoutError:
                self._record_failure()
                if attempt < retries:
                    await asyncio.sleep(2 ** attempt)
                continue
            except Exception as e:
                self._record_failure()
                print(f"API request error: {e}")
                if attempt < retries:
                    await asyncio.sleep(2 ** attempt)
                continue

        return None

    def _record_success(self):
        """Record successful API call"""
        self.failure_count = 0
        self.circuit_open = False
        if self.status != APIStatus.HEALTHY:
            self.status = APIStatus.HEALTHY
            print("✅ GW2 API status: HEALTHY")

    def _record_failure(self):
        """Record failed API call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now(IRAN_TIMEZONE)

        if self.failure_count >= API_CIRCUIT_BREAKER_THRESHOLD:
            self.circuit_open = True
            self.status = APIStatus.DOWN
            print(f"❌ GW2 API status: DOWN (Circuit breaker opened)")
        elif self.failure_count > 1:
            self.status = APIStatus.DEGRADED
            print(f"⚠️ GW2 API status: DEGRADED ({self.failure_count} failures)")

    def is_circuit_open(self) -> bool:
        """Check if circuit breaker is open"""
        if not self.circuit_open:
            return False

        # Check if enough time has passed to try again
        if self.last_failure_time:
            time_since_failure = (datetime.now(IRAN_TIMEZONE) - self.last_failure_time).total_seconds()
            if time_since_failure > API_CIRCUIT_BREAKER_TIMEOUT:
                self.circuit_open = False
                print("🔄 Circuit breaker reset - attempting API reconnection")
                return False

        return True

    async def get_world_bosses(self) -> Optional[List[Dict]]:
        """Fetch world boss data from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(GW2_WORLD_BOSSES)

    async def get_events(self) -> Optional[List[Dict]]:
        """Fetch event data from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(GW2_EVENTS)

    def get_status(self) -> Dict[str, Any]:
        """Get current API status information"""
        return {
            "status": self.status.value,
            "failure_count": self.failure_count,
            "circuit_open": self.circuit_open,
            "last_failure": self.last_failure_time.isoformat() if self.last_failure_time else None
        }

# Helper functions for boss timing
def get_next_spawn_time(boss_id: str) -> Optional[datetime]:
    """Calculate the next spawn time for a boss"""
    if boss_id not in WORLD_BOSSES:
        return None

    boss = WORLD_BOSSES[boss_id]

    # Work with UTC time for calculations, then convert to Iran time for display
    utc_now = datetime.utcnow()

    # Find next spawn time in UTC
    for hour in boss["schedule"]:
        spawn_time_utc = utc_now.replace(hour=hour, minute=0, second=0, microsecond=0)

        # If this spawn time hasn't passed today (in UTC)
        if spawn_time_utc > utc_now:
            # Convert to Iran timezone for return
            spawn_time_iran = spawn_time_utc.replace(tzinfo=pytz.UTC).astimezone(IRAN_TIMEZONE)
            return spawn_time_iran

    # If no spawn time today, get first spawn time tomorrow (in UTC)
    tomorrow_utc = utc_now + timedelta(days=1)
    first_hour = min(boss["schedule"])
    spawn_time_utc = tomorrow_utc.replace(hour=first_hour, minute=0, second=0, microsecond=0)

    # Convert to Iran timezone for return
    spawn_time_iran = spawn_time_utc.replace(tzinfo=pytz.UTC).astimezone(IRAN_TIMEZONE)
    return spawn_time_iran

def get_time_until_spawn(boss_id: str) -> Optional[timedelta]:
    """Get time remaining until next spawn"""
    next_spawn = get_next_spawn_time(boss_id)
    if next_spawn:
        return next_spawn - datetime.now(IRAN_TIMEZONE)
    return None

async def send_alert_message(channel_id: int, boss_id: str, alert_type: str):
    """Send alert message to Discord channel"""
    try:
        channel = bot.get_channel(channel_id)
        if not channel:
            print(f"❌ Could not find channel {channel_id}")
            return

        boss = WORLD_BOSSES.get(boss_id)
        if not boss:
            print(f"❌ Unknown boss: {boss_id}")
            return

        next_spawn = get_next_spawn_time(boss_id)
        if not next_spawn:
            return

        embed = discord.Embed(color=0x00ff00)

        if alert_type == "10min":
            embed.title = f"🔔 10 Minute Warning!"
            embed.description = f"{boss['emoji']} **{boss['name']}** spawns in 10 minutes!"
            embed.color = 0xffaa00
        elif alert_type == "5min":
            embed.title = f"⚠️ 5 Minute Warning!"
            embed.description = f"{boss['emoji']} **{boss['name']}** spawns in 5 minutes!"
            embed.color = 0xff6600
        elif alert_type == "spawn":
            embed.title = f"🚨 SPAWNING NOW!"
            embed.description = f"{boss['emoji']} **{boss['name']}** is spawning now!"
            embed.color = 0xff0000

        embed.add_field(name="Location", value=boss["map"], inline=True)
        embed.add_field(name="Duration", value=f"{boss['duration']} minutes", inline=True)

        spawn_time_str = next_spawn.strftime("%H:%M Iran Time")
        embed.add_field(name="Spawn Time", value=spawn_time_str, inline=True)

        embed.timestamp = datetime.now(IRAN_TIMEZONE)
        embed.set_footer(text="GW2 World Boss Timer")

        await channel.send(embed=embed)
        print(f"✅ Sent {alert_type} alert for {boss['name']}")

    except Exception as e:
        print(f"❌ Error sending alert: {e}")
        traceback.print_exc()

async def boss_timer_loop():
    """Main background task that monitors boss spawns and sends alerts"""
    print("🔄 Starting boss timer loop...")

    while True:
        try:
            await asyncio.sleep(30)  # Check every 30 seconds

            if not bot.is_ready():
                continue

            now = datetime.now(IRAN_TIMEZONE)
            current_time_key = now.strftime("%Y-%m-%d-%H-%M")

            for boss_id, boss_data in WORLD_BOSSES.items():
                next_spawn = get_next_spawn_time(boss_id)
                if not next_spawn:
                    continue

                time_until = (next_spawn - now).total_seconds()

                # Initialize alert tracking for this boss if needed
                if boss_id not in sent_alerts:
                    sent_alerts[boss_id] = {}

                # 10 minute warning (between 10:00 and 9:30 before spawn)
                if 570 <= time_until <= 600:  # 9.5 to 10 minutes
                    alert_key = f"10min-{next_spawn.strftime('%Y-%m-%d-%H-%M')}"
                    if alert_key not in sent_alerts[boss_id]:
                        await send_alert_message(config.TARGET_CHANNEL_ID, boss_id, "10min")
                        sent_alerts[boss_id][alert_key] = now

                # 5 minute warning (between 5:00 and 4:30 before spawn)
                elif 270 <= time_until <= 300:  # 4.5 to 5 minutes
                    alert_key = f"5min-{next_spawn.strftime('%Y-%m-%d-%H-%M')}"
                    if alert_key not in sent_alerts[boss_id]:
                        await send_alert_message(config.TARGET_CHANNEL_ID, boss_id, "5min")
                        sent_alerts[boss_id][alert_key] = now

                # Spawn notification (between 0 and 30 seconds before spawn)
                elif 0 <= time_until <= 30:
                    alert_key = f"spawn-{next_spawn.strftime('%Y-%m-%d-%H-%M')}"
                    if alert_key not in sent_alerts[boss_id]:
                        await send_alert_message(config.TARGET_CHANNEL_ID, boss_id, "spawn")
                        sent_alerts[boss_id][alert_key] = now

            # Clean up old alert records (older than 24 hours)
            cutoff_time = now - timedelta(hours=24)
            for boss_id in list(sent_alerts.keys()):
                for alert_key in list(sent_alerts[boss_id].keys()):
                    if sent_alerts[boss_id][alert_key] < cutoff_time:
                        del sent_alerts[boss_id][alert_key]

                # Remove empty boss entries
                if not sent_alerts[boss_id]:
                    del sent_alerts[boss_id]

        except Exception as e:
            print(f"❌ Error in boss timer loop: {e}")
            traceback.print_exc()
            await asyncio.sleep(60)  # Wait longer on error

# Global API manager instance
api_manager = None

# Slash Commands
@bot.slash_command(name="help", description="Show available commands")
async def help_command(ctx):
    """Display help information"""
    embed = discord.Embed(
        title="🎮 Guild Wars 2 World Boss Bot",
        description="Track world boss spawns and get notifications!",
        color=0x00ff00
    )

    embed.add_field(
        name="📋 Commands",
        value=(
            "`/help` - Show this help message\n"
            "`/next_spawns` - Show next 5 bosses spawning soon\n"
            "`/boss_list` - List all world bosses\n"
            "`/next_boss [boss_name]` - Get detailed info for a specific boss\n"
            "`/validate_data` - Validate bot data accuracy\n"
            "`/api_status` - Check GW2 API connection status"
        ),
        inline=False
    )

    embed.add_field(
        name="🔔 Auto Alerts",
        value=(
            "The bot automatically sends alerts:\n"
            "• 10 minutes before spawn\n"
            "• 5 minutes before spawn\n"
            "• When boss spawns"
        ),
        inline=False
    )

    embed.add_field(
        name="ℹ️ Info",
        value=f"Configured for channel: <#{config.TARGET_CHANNEL_ID}>",
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="GW2 World Boss Timer")

    await ctx.respond(embed=embed)

@bot.slash_command(name="boss_list", description="List all world bosses and their next spawn times")
async def boss_list(ctx):
    """Display list of all world bosses"""
    embed = discord.Embed(
        title="🐉 World Boss Schedule",
        description="Next spawn times for all world bosses",
        color=0x0099ff
    )

    boss_times = []
    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = get_time_until_spawn(boss_id)
            if time_until:
                hours = int(time_until.total_seconds() // 3600)
                minutes = int((time_until.total_seconds() % 3600) // 60)
                time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"

                boss_times.append({
                    'name': boss_data['name'],
                    'emoji': boss_data['emoji'],
                    'map': boss_data['map'],
                    'spawn_time': next_spawn.strftime("%H:%M"),
                    'time_until': time_str,
                    'seconds_until': time_until.total_seconds()
                })

    # Sort by time until spawn
    boss_times.sort(key=lambda x: x['seconds_until'])

    # Split into chunks for multiple fields
    chunk_size = 8
    for i in range(0, len(boss_times), chunk_size):
        chunk = boss_times[i:i + chunk_size]
        field_value = ""

        for boss in chunk:
            field_value += f"{boss['emoji']} **{boss['name']}**\n"
            field_value += f"📍 {boss['map']} • ⏰ {boss['spawn_time']} Iran Time • ⏳ {boss['time_until']}\n\n"

        field_name = "🕐 Next Spawns" if i == 0 else "🕐 More Spawns"
        embed.add_field(name=field_name, value=field_value, inline=False)

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Times shown in Iran Time (UTC+3:30)")

    await ctx.respond(embed=embed)

async def boss_autocomplete(ctx: discord.AutocompleteContext):
    """Autocomplete function for boss names"""
    user_input = ctx.value.lower() if ctx.value else ""

    # Create list of boss choices with emoji and name
    choices = []
    for boss_id, boss_data in WORLD_BOSSES.items():
        boss_display_name = f"{boss_data['emoji']} {boss_data['name']}"

        # If user typed something, filter by it
        if user_input:
            if (user_input in boss_data['name'].lower() or
                user_input in boss_id.lower() or
                user_input in boss_data['map'].lower()):
                choices.append(boss_display_name)
        else:
            choices.append(boss_display_name)

    # Sort choices alphabetically and limit to 25 (Discord limit)
    choices.sort()
    return choices[:25]

@bot.slash_command(name="next_boss", description="Get next spawn time for a specific boss")
async def next_boss(ctx, boss_name: discord.Option(str, "Choose a world boss", autocomplete=boss_autocomplete)):
    """Get next spawn time for a specific boss"""
    # Extract the actual boss name from the display format "emoji name"
    # Remove emoji and extra spaces
    clean_boss_name = boss_name
    if " " in boss_name:
        # Remove the emoji part (first part before space)
        parts = boss_name.split(" ", 1)
        if len(parts) > 1:
            clean_boss_name = parts[1]

    # Find boss by name (case insensitive, partial match)
    boss_name_lower = clean_boss_name.lower()
    found_boss = None
    found_boss_id = None

    for boss_id, boss_data in WORLD_BOSSES.items():
        if (boss_name_lower in boss_data['name'].lower() or
            boss_name_lower in boss_id.lower() or
            boss_data['name'].lower() == boss_name_lower):
            found_boss = boss_data
            found_boss_id = boss_id
            break

    if not found_boss:
        embed = discord.Embed(
            title="❌ Boss Not Found",
            description=f"Could not find a boss matching '{clean_boss_name}'",
            color=0xff0000
        )
        embed.add_field(
            name="💡 Tip",
            value="Use `/boss_list` to see all available bosses",
            inline=False
        )
        await ctx.respond(embed=embed)
        return

    next_spawn = get_next_spawn_time(found_boss_id)
    time_until = get_time_until_spawn(found_boss_id)

    if not next_spawn or not time_until:
        embed = discord.Embed(
            title="❌ Error",
            description="Could not calculate spawn time",
            color=0xff0000
        )
        await ctx.respond(embed=embed)
        return

    embed = discord.Embed(
        title=f"{found_boss['emoji']} {found_boss['name']}",
        color=0x00ff00
    )

    hours = int(time_until.total_seconds() // 3600)
    minutes = int((time_until.total_seconds() % 3600) // 60)
    time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"

    embed.add_field(name="📍 Location", value=found_boss['map'], inline=True)
    embed.add_field(name="⏰ Next Spawn", value=next_spawn.strftime("%H:%M Iran Time"), inline=True)
    embed.add_field(name="⏳ Time Until", value=time_str, inline=True)
    embed.add_field(name="⏱️ Duration", value=f"{found_boss['duration']} minutes", inline=True)
    embed.add_field(name="🎯 Difficulty", value=found_boss.get('difficulty', 'Unknown'), inline=True)

    # Pre-event information
    pre_event_time = found_boss.get('pre_event_time', 0)
    if pre_event_time > 0:
        embed.add_field(name="⚡ Pre-events", value=f"Start {pre_event_time} min before", inline=True)

    # Show schedule
    schedule_str = ", ".join([f"{hour:02d}:00" for hour in found_boss['schedule']])
    embed.add_field(name="📅 Daily Schedule (UTC)", value=schedule_str, inline=False)

    # Add note about timezone
    embed.add_field(
        name="🌍 Timezone Info",
        value="Schedule shows UTC times. Display times are converted to Iran Time (UTC+3:30)",
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Times shown in Iran Time (UTC+3:30)")

    await ctx.respond(embed=embed)

@bot.slash_command(name="next_spawns", description="Show the next 5 world bosses spawning soon")
async def next_spawns(ctx):
    """Show the next few world bosses spawning soon"""
    boss_times = []
    now = datetime.now(IRAN_TIMEZONE)

    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = get_time_until_spawn(boss_id)
            if time_until:
                boss_times.append({
                    'name': boss_data['name'],
                    'emoji': boss_data['emoji'],
                    'map': boss_data['map'],
                    'spawn_time': next_spawn.strftime("%H:%M"),
                    'time_until_seconds': time_until.total_seconds(),
                    'time_until': time_until
                })

    # Sort by time until spawn and take first 5
    boss_times.sort(key=lambda x: x['time_until_seconds'])
    next_5_bosses = boss_times[:5]

    embed = discord.Embed(
        title="⏰ Next 5 World Boss Spawns",
        description="Upcoming world boss spawns in chronological order",
        color=0x0099ff
    )

    for i, boss in enumerate(next_5_bosses, 1):
        hours = int(boss['time_until'].total_seconds() // 3600)
        minutes = int((boss['time_until'].total_seconds() % 3600) // 60)
        time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"

        # Add urgency indicators
        if boss['time_until_seconds'] <= 300:  # 5 minutes
            urgency = "🚨 SPAWNING SOON!"
        elif boss['time_until_seconds'] <= 600:  # 10 minutes
            urgency = "⚠️ Warning!"
        else:
            urgency = "🕐 Upcoming"

        embed.add_field(
            name=f"{i}. {boss['emoji']} {boss['name']} {urgency}",
            value=f"📍 **{boss['map']}**\n⏰ Spawns at **{boss['spawn_time']} Iran Time**\n⏳ In **{time_str}**",
            inline=False
        )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Use /next_boss to get detailed info about a specific boss")

    await ctx.respond(embed=embed)

@bot.slash_command(name="validate_data", description="Validate bot data against current time and known schedules")
async def validate_data(ctx):
    """Validate the accuracy of our world boss data"""
    embed = discord.Embed(
        title="🔍 Data Validation Report",
        description="Checking accuracy of world boss schedules and timings",
        color=0x0099ff
    )

    now = datetime.now(IRAN_TIMEZONE)
    utc_now = datetime.utcnow()

    # Check timezone conversion
    embed.add_field(
        name="🌍 Timezone Check",
        value=f"Current Iran Time: {now.strftime('%H:%M:%S')}\nCurrent UTC: {utc_now.strftime('%H:%M:%S')}\nOffset: +3:30",
        inline=False
    )

    # Validate next few spawns
    upcoming_spawns = []
    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = get_time_until_spawn(boss_id)
            if time_until and time_until.total_seconds() <= 7200:  # Next 2 hours
                upcoming_spawns.append({
                    'name': boss_data['name'],
                    'spawn_time': next_spawn,
                    'time_until': time_until.total_seconds()
                })

    # Sort by spawn time
    upcoming_spawns.sort(key=lambda x: x['time_until'])

    if upcoming_spawns:
        validation_text = ""
        for spawn in upcoming_spawns[:5]:  # Show next 5
            minutes_until = int(spawn['time_until'] // 60)
            spawn_time_iran = spawn['spawn_time'].strftime('%H:%M Iran Time')
            validation_text += f"• {spawn['name']}: {spawn_time_iran} (in {minutes_until}m)\n"

        embed.add_field(
            name="⏰ Next Spawns (Validation)",
            value=validation_text,
            inline=False
        )

    # Data accuracy notes
    embed.add_field(
        name="✅ Data Sources",
        value=(
            "• Schedule based on official GW2 community timers\n"
            "• Times converted from UTC to Iran Time (+3:30)\n"
            "• Major bosses: Every 3 hours\n"
            "• Regular bosses: Every 2 hours\n"
            "• Pre-event times included where applicable"
        ),
        inline=False
    )

    embed.add_field(
        name="⚠️ Important Notes",
        value=(
            "• Times may vary by 1-2 minutes due to server lag\n"
            "• Pre-events must complete for boss to spawn\n"
            "• Some bosses require map population\n"
            "• Triple Trouble requires organized groups"
        ),
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Data validated against official sources")

    await ctx.respond(embed=embed)

@bot.slash_command(name="api_status", description="Check GW2 API connection status")
async def api_status(ctx):
    """Display GW2 API status"""
    global api_manager

    if not api_manager:
        embed = discord.Embed(
            title="❌ API Manager Not Initialized",
            color=0xff0000
        )
        await ctx.respond(embed=embed)
        return

    status_info = api_manager.get_status()

    # Determine color based on status
    color_map = {
        "healthy": 0x00ff00,
        "degraded": 0xffaa00,
        "down": 0xff0000
    }
    color = color_map.get(status_info["status"], 0x888888)

    # Status emoji
    emoji_map = {
        "healthy": "✅",
        "degraded": "⚠️",
        "down": "❌"
    }
    emoji = emoji_map.get(status_info["status"], "❓")

    embed = discord.Embed(
        title=f"{emoji} GW2 API Status",
        color=color
    )

    embed.add_field(
        name="Status",
        value=status_info["status"].upper(),
        inline=True
    )

    embed.add_field(
        name="Failure Count",
        value=str(status_info["failure_count"]),
        inline=True
    )

    embed.add_field(
        name="Circuit Breaker",
        value="OPEN" if status_info["circuit_open"] else "CLOSED",
        inline=True
    )

    if status_info["last_failure"]:
        last_failure = datetime.fromisoformat(status_info["last_failure"])
        embed.add_field(
            name="Last Failure",
            value=last_failure.strftime("%Y-%m-%d %H:%M:%S Iran Time"),
            inline=False
        )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="GW2 World Boss Timer")

    await ctx.respond(embed=embed)

@bot.event
async def setup_hook():
    """Initialize resources when the bot starts up"""
    global api_manager
    api_manager = GW2APIManager()
    # Start background tasks
    bot.loop.create_task(boss_timer_loop())

    # Sync slash commands
    try:
        synced = await bot.sync_commands()
        print(f"✅ Synced {len(synced)} slash commands")
    except Exception as e:
        print(f"❌ Failed to sync commands: {e}")

@bot.event
async def on_ready():
    """Called when the bot is ready"""
    print(f'✅ Logged in as {bot.user} (ID: {bot.user.id})')
    print('------')

    # Set custom status
    activity = discord.Activity(
        type=discord.ActivityType.watching,
        name=f"for world bosses | /help"
    )
    await bot.change_presence(activity=activity)

    # Send startup notification to configured channel
    try:
        channel = bot.get_channel(config.TARGET_CHANNEL_ID)
        if channel:
            embed = discord.Embed(
                title="🤖 Bot Online",
                description="GW2 World Boss Timer is now online and monitoring boss spawns!",
                color=0x00ff00
            )
            embed.add_field(
                name="🔔 Auto Alerts Enabled",
                value="You'll receive notifications 10min, 5min, and at spawn time",
                inline=False
            )
            embed.timestamp = datetime.now(IRAN_TIMEZONE)
            embed.set_footer(text="Use /help to see available commands")
            await channel.send(embed=embed)
            print(f"✅ Sent startup notification to channel {config.TARGET_CHANNEL_ID}")
    except Exception as e:
        print(f"⚠️ Could not send startup notification: {e}")

    print("🔔 Auto alerts are now active!")
    print(f"📍 Monitoring channel: {config.TARGET_CHANNEL_ID}")
    print("🚀 Bot is ready!")

@bot.event
async def on_application_command_error(ctx, error):
    """Handle slash command errors"""
    print(f"❌ Command error in {ctx.command}: {error}")

    embed = discord.Embed(
        title="❌ Command Error",
        description="An error occurred while processing your command.",
        color=0xff0000
    )

    if isinstance(error, commands.CommandOnCooldown):
        embed.description = f"Command is on cooldown. Try again in {error.retry_after:.1f} seconds."
    elif isinstance(error, commands.MissingPermissions):
        embed.description = "You don't have permission to use this command."
    else:
        embed.description = "An unexpected error occurred. Please try again later."

    try:
        await ctx.respond(embed=embed, ephemeral=True)
    except:
        pass  # If we can't respond, just log the error

@bot.event
async def on_disconnect():
    """Cleanup when the bot disconnects"""
    print("⚠️ Bot disconnected from Discord")
    global api_manager
    if api_manager:
        await api_manager.close()

@bot.event
async def on_shutdown():
    """Cleanup when the bot shuts down"""
    print("🔄 Bot shutting down...")
    global api_manager
    if api_manager:
        await api_manager.close()
    await bot.http.close()

def main():
    # Load environment variables from .env file
    load_dotenv()

    # Get the bot token from environment variable
    TOKEN = os.getenv("DISCORD_TOKEN")

    if not TOKEN:
        print("❌ Error: No DISCORD_TOKEN found in environment variables or .env file")
        print("💡 Please create a .env file with: DISCORD_TOKEN=your_bot_token_here")
        return

    print("🚀 Starting Guild Wars 2 World Boss Discord Bot...")
    print(f"📍 Target Guild ID: {config.TARGET_GUILD_ID}")
    print(f"📍 Target Channel ID: {config.TARGET_CHANNEL_ID}")

    # Set up signal handlers for graceful shutdown
    import signal

    def handle_shutdown(signum, frame):
        print("\n🔄 Shutting down gracefully...")
        # This will trigger the cleanup in the close() handler
        bot.loop.create_task(bot.close())

    # Register signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, handle_shutdown)

    # Start the bot
    try:
        bot.run(TOKEN)
    except KeyboardInterrupt:
        print("\n🔄 Bot stopped by user")
    except discord.LoginFailure:
        print("❌ Invalid Discord token! Please check your .env file")
        sys.exit(1)
    except Exception as e:
        current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S Iran Time")
        print("\n" + "=" * 60)
        print("💥 UNEXPECTED ERROR OCCURRED")
        print("=" * 60)
        print(f"📅 Error Time: {current_time}")
        print(f"❌ Error: {e}")
        print(f"🔍 Error Type: {type(e).__name__}")
        traceback.print_exc()
        print("🔄 Bot will attempt to restart...")
        print("=" * 60)
        sys.exit(1)

# Run the main function
if __name__ == "__main__":
    main()
